# MinERU 评估系统

这是一个用于评估MinERU输出质量的系统，通过比较truth（真实标准）和output（模型输出）文件，分别使用NED（文本）和TEDS（表格）指标进行评估。

## 功能特性

- **文本评估**: 使用NED（Normalized Edit Distance）评估文本内容相似度
- **表格评估**: 使用TEDS（Tree Edit Distance based Similarity）评估表格结构和内容
- **批量处理**: 支持整个目录的批量评估
- **详细报告**: 提供详细的评估指标和统计信息
- **灵活配置**: 支持多种评估参数配置

## 安装依赖

```bash
pip install apted python-Levenshtein lxml
```

## 使用方法

### 1. 评估整个目录

```bash
python -m eval.eval_main --truth-dir truth --output-dir output
```

### 2. 评估单个文件对

```bash
python -m eval.eval_main --truth-file truth/data.md --output-file output/data.md
```

### 3. 生成详细报告并保存为JSON

```bash
python -m eval.eval_main --truth-dir truth --output-dir output --detailed --output-json results.json
```

### 4. 只评估表格结构（忽略内容）

```bash
python -m eval.eval_main --truth-dir truth --output-dir output --table-structure-only
```

### 5. 文本评估时忽略大小写

```bash
python -m eval.eval_main --truth-dir truth --output-dir output --text-ignore-case
```

## 评估指标

### NED (Normalized Edit Distance)
- **范围**: 0-1，1表示完全相同
- **用途**: 评估文本内容的相似度
- **特性**: 支持空白字符标准化、大小写忽略等预处理选项

### TEDS (Tree Edit Distance based Similarity)
- **范围**: 0-1，1表示完全相同
- **用途**: 评估HTML表格的结构和内容相似度
- **特性**: 支持结构模式（只比较结构）和完整模式（比较结构+内容）

## 输出格式

### 控制台输出示例

```
============================================================
评估结果汇总
============================================================
总文件数: 2
成功评估: 2
评估失败: 0

综合评估分数:
  平均分数: 0.9998
  最高分数: 1.0000
  最低分数: 0.9995

文本评估 (NED):
  平均分数: 0.9996
  最高分数: 1.0000
  最低分数: 0.9991

表格评估 (TEDS):
  平均分数: 1.0000
  最高分数: 1.0000
  最低分数: 1.0000
```

### JSON输出结构

```json
{
  "total_files": 2,
  "individual_results": [
    {
      "files": {
        "truth": "truth/data.md",
        "output": "output/data.md"
      },
      "text_evaluation": {
        "ned_score": 0.9991,
        "edit_distance": 1,
        "char_accuracy": 0.9992
      },
      "table_evaluation": {
        "table_count": 10,
        "avg_teds_score": 1.0,
        "individual_scores": [...]
      },
      "overall_score": {
        "weighted_score": 0.9995,
        "text_score": 0.9991,
        "table_score": 1.0
      }
    }
  ],
  "summary": {
    "avg_weighted_score": 0.9998,
    "text_metrics": {...},
    "table_metrics": {...}
  }
}
```

## 命令行参数

### 输入选项
- `--truth-dir`: 真实标准文件目录
- `--truth-file`: 单个真实标准文件
- `--output-dir`: 输出文件目录
- `--output-file`: 单个输出文件

### 评估选项
- `--text-normalize-whitespace`: 标准化空白字符（默认启用）
- `--text-ignore-case`: 忽略大小写
- `--table-structure-only`: 只评估表格结构
- `--table-ignore-nodes`: 忽略指定HTML节点

### 输出选项
- `--output-json`: 保存结果为JSON文件
- `--detailed`: 显示详细结果
- `--quiet`: 只显示汇总结果

## 文件匹配规则

系统会自动匹配truth和output目录中的对应文件：

1. **精确匹配**: 文件名完全相同
2. **清理匹配**: 移除时间戳和常见后缀后匹配
3. **部分匹配**: 基于包含关系匹配
4. **模糊匹配**: 基于字符相似度匹配（阈值>0.6）

## 注意事项

1. 确保truth和output目录中的文件格式一致（都是.md文件）
2. 表格必须是有效的HTML格式
3. 大文件评估可能需要较长时间
4. 建议在评估前备份重要文件

## 错误处理

系统会处理以下常见错误：
- 文件不存在或无法读取
- HTML表格格式错误
- 内容提取失败
- 评估计算异常

所有错误都会在结果中详细记录，不会中断整个评估过程。
