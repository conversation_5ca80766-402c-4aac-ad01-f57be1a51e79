#!/usr/bin/env python3
"""
MinERU Content List 处理器 - 主程序入口

用法:
    python main.py                                    # 处理list文件夹下的第一个文件
    python main.py --file path/to/file.json         # 处理指定文件
    python main.py --help                           # 显示帮助信息
"""

import argparse
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config import LIST_DIR, OUTPUT_DIR
from utils.logger import get_logger, setup_logger
from utils.file_utils import (
    load_json,
    save_markdown,
    find_content_list_files,
    generate_output_path,
    validate_content_list,
)
from processors.content_processor import content_processor
from processors.markdown_generator import markdown_generator
from utils.llm_logger import llm_call_logger

# 设置日志
logger = setup_logger("main")


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="MinERU Content List 处理器 - 将content_list.json转换为Markdown",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    python main.py
    python main.py --file list/example_content_list.json
    python main.py --file list/example_content_list.json --output custom_output.md
    python main.py --no-metadata

提示词管理:
    提示词文件位于: prompts/llm_prompts.md
    修改提示词后需要重启程序生效
    运行 python test_prompts.py 测试提示词系统
        """,
    )

    parser.add_argument(
        "--file", "-f", type=str, help="指定要处理的content_list.json文件路径"
    )

    parser.add_argument(
        "--output", "-o", type=str, help="指定输出Markdown文件路径（可选）"
    )

    parser.add_argument(
        "--with-metadata", action="store_true", help="生成文档元数据头部"
    )

    parser.add_argument("--verbose", "-v", action="store_true", help="显示详细日志信息")

    return parser.parse_args()


def find_default_file() -> Optional[Path]:
    """查找默认的content_list文件"""
    logger.info(f"在 {LIST_DIR} 中查找content_list文件")

    files = find_content_list_files(LIST_DIR)

    if not files:
        logger.error(f"在 {LIST_DIR} 中未找到任何content_list.json文件")
        return None

    default_file = files[0]
    logger.info(f"找到 {len(files)} 个文件，使用第一个: {default_file.name}")

    return default_file


def process_single_file(
    input_file: Path, output_file: Optional[Path] = None, include_metadata: bool = False
) -> bool:
    """
    处理单个content_list文件

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径（可选）
        include_metadata: 是否包含元数据头部

    Returns:
        处理是否成功
    """
    try:
        logger.info(f"开始处理文件: {input_file}")

        # 检查输入文件
        if not input_file.exists():
            logger.error(f"输入文件不存在: {input_file}")
            return False

        # 生成输出文件路径
        if output_file is None:
            output_file = generate_output_path(input_file, OUTPUT_DIR)

        logger.info(f"输出文件: {output_file}")

        # 加载JSON数据
        logger.info("加载JSON数据...")
        content_list = load_json(input_file)

        # 验证数据格式
        logger.info("验证数据格式...")
        if not validate_content_list(content_list):
            logger.error("数据格式验证失败")
            return False

        # 记录原始统计信息
        original_count = len(content_list)
        logger.info(f"原始数据包含 {original_count} 个元素")

        # 处理内容
        logger.info("开始处理内容...")
        processed_list = content_processor.process_content_list(content_list)

        # 获取处理统计信息
        stats = content_processor.get_processing_stats(content_list, processed_list)

        # 生成Markdown
        logger.info("生成Markdown...")
        markdown_content = markdown_generator.generate_markdown(processed_list)

        # 添加元数据头部
        if include_metadata:
            metadata_header = markdown_generator.generate_metadata_header(
                stats, input_file.name
            )
            markdown_content = metadata_header + "\n\n" + markdown_content

        # 保存文件
        logger.info("保存Markdown文件...")
        save_markdown(markdown_content, output_file)

        # 保存LLM调用记录
        filename_prefix = Path(input_file).stem
        llm_call_logger.save_session_log(filename_prefix)

        # 输出处理结果
        logger.info("=" * 50)
        logger.info("处理完成！")
        logger.info(f"输入文件: {input_file}")
        logger.info(f"输出文件: {output_file}")
        logger.info(f"原始元素: {stats['original_count']}")
        logger.info(f"处理后元素: {stats['processed_count']}")
        logger.info(f"合并元素: {stats['elements_merged']}")
        logger.info(f"表格合并: {stats['tables_merged']}")
        logger.info(f"文本拼接: {stats['texts_joined']}")
        logger.info(f"页面范围: {stats['page_range']}")
        logger.info("=" * 50)

        return True

    except Exception as e:
        logger.error(f"处理文件时发生错误: {e}")
        return False


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        # 设置日志级别
        if args.verbose:
            import logging

            logging.getLogger().setLevel(logging.DEBUG)

        logger.info("MinERU Content List 处理器启动")

        # 确定输入文件
        if args.file:
            input_file = Path(args.file)
            if not input_file.is_absolute():
                input_file = Path.cwd() / input_file
        else:
            input_file = find_default_file()
            if input_file is None:
                logger.error("未找到可处理的文件")
                sys.exit(1)

        # 确定输出文件
        output_file = None
        if args.output:
            output_file = Path(args.output)
            if not output_file.is_absolute():
                output_file = Path.cwd() / output_file

        # 处理文件
        success = process_single_file(
            input_file=input_file,
            output_file=output_file,
            include_metadata=args.with_metadata,
        )

        if success:
            logger.info("程序执行成功")
            sys.exit(0)
        else:
            logger.error("程序执行失败")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
