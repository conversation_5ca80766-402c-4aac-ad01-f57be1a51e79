"""
LLM调用记录器
"""
import json
import os
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

class LLMCallLogger:
    """LLM调用记录器"""
    
    def __init__(self, output_dir: str = "llm_logs"):
        """
        初始化LLM调用记录器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.calls: List[Dict[str, Any]] = []
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def log_call(
        self,
        call_type: str,
        system_prompt: str,
        user_prompt: str,
        response: str,
        result: Any,
        context: Dict[str, Any] = None
    ):
        """
        记录一次LLM调用
        
        Args:
            call_type: 调用类型 (table_merge, text_join)
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            response: LLM原始响应
            result: 解析后的结果
            context: 上下文信息
        """
        call_record = {
            "timestamp": datetime.now().isoformat(),
            "call_id": len(self.calls) + 1,
            "call_type": call_type,
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "llm_response": response,
            "parsed_result": result,
            "context": context or {}
        }
        
        self.calls.append(call_record)
    
    def save_session_log(self, filename_prefix: str = None):
        """
        保存当前会话的所有LLM调用记录
        
        Args:
            filename_prefix: 文件名前缀
        """
        if not self.calls:
            return
        
        # 生成文件名
        if filename_prefix:
            filename = f"{filename_prefix}_{self.session_id}_llm_calls.json"
        else:
            filename = f"{self.session_id}_llm_calls.json"
        
        filepath = self.output_dir / filename
        
        # 保存详细记录
        session_data = {
            "session_id": self.session_id,
            "total_calls": len(self.calls),
            "call_types": self._get_call_statistics(),
            "calls": self.calls
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
        
        # 保存简化的摘要
        summary_file = filepath.with_suffix('.summary.json')
        summary_data = {
            "session_id": self.session_id,
            "total_calls": len(self.calls),
            "call_types": self._get_call_statistics(),
            "calls_summary": [
                {
                    "call_id": call["call_id"],
                    "call_type": call["call_type"],
                    "timestamp": call["timestamp"],
                    "result": call["parsed_result"],
                    "context_summary": self._summarize_context(call["context"])
                }
                for call in self.calls
            ]
        }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        print(f"LLM调用记录已保存:")
        print(f"  详细记录: {filepath}")
        print(f"  摘要记录: {summary_file}")
        print(f"  总调用次数: {len(self.calls)}")
    
    def _get_call_statistics(self) -> Dict[str, int]:
        """获取调用统计"""
        stats = {}
        for call in self.calls:
            call_type = call["call_type"]
            stats[call_type] = stats.get(call_type, 0) + 1
        return stats
    
    def _summarize_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """保留完整的上下文信息（不再截断）"""
        if not context:
            return {}

        # 直接返回完整的上下文信息，不进行截断
        return context.copy()
    
    def clear_session(self):
        """清空当前会话记录"""
        self.calls.clear()
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")


# 创建全局LLM调用记录器实例
llm_call_logger = LLMCallLogger()
