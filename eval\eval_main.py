"""
评估系统主入口 - 命令行接口
"""

import argparse
import json
import sys
from pathlib import Path
from typing import Optional

from .file_matcher import FileMatcher
from .evaluator import Evaluator


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="MinERU评估系统 - 比较truth和output文件的文本和表格内容",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
    # 评估整个目录
    python -m eval.eval_main --truth-dir truth --output-dir output
    
    # 评估单个文件对
    python -m eval.eval_main --truth-file truth/data.md --output-file output/data.md
    
    # 生成详细报告
    python -m eval.eval_main --truth-dir truth --output-dir output --detailed --output-json results.json
    
    # 只评估表格结构
    python -m eval.eval_main --truth-dir truth --output-dir output --table-structure-only
        """
    )
    
    # 输入选项
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        '--truth-dir', type=str,
        help='真实标准文件目录'
    )
    input_group.add_argument(
        '--truth-file', type=str,
        help='单个真实标准文件路径'
    )
    
    parser.add_argument(
        '--output-dir', type=str,
        help='输出文件目录（与--truth-dir配合使用）'
    )
    parser.add_argument(
        '--output-file', type=str,
        help='单个输出文件路径（与--truth-file配合使用）'
    )
    
    # 评估选项
    parser.add_argument(
        '--text-normalize-whitespace', action='store_true', default=True,
        help='文本评估时标准化空白字符（默认启用）'
    )
    parser.add_argument(
        '--text-ignore-case', action='store_true', default=False,
        help='文本评估时忽略大小写'
    )
    parser.add_argument(
        '--table-structure-only', action='store_true', default=False,
        help='表格评估时只考虑结构，忽略内容'
    )
    parser.add_argument(
        '--table-ignore-nodes', type=str,
        help='表格评估时要忽略的HTML节点（逗号分隔）'
    )
    
    # 输出选项
    parser.add_argument(
        '--output-json', type=str,
        help='将结果保存为JSON文件'
    )
    parser.add_argument(
        '--detailed', action='store_true', default=False,
        help='显示详细的评估结果'
    )
    parser.add_argument(
        '--quiet', action='store_true', default=False,
        help='只显示汇总结果'
    )
    
    return parser.parse_args()


def validate_arguments(args: argparse.Namespace) -> bool:
    """验证命令行参数"""
    if args.truth_dir:
        if not args.output_dir:
            print("错误: 使用--truth-dir时必须指定--output-dir")
            return False
        
        truth_path = Path(args.truth_dir)
        output_path = Path(args.output_dir)
        
        if not truth_path.exists():
            print(f"错误: 真实标准目录不存在: {truth_path}")
            return False
        
        if not output_path.exists():
            print(f"错误: 输出目录不存在: {output_path}")
            return False
    
    elif args.truth_file:
        if not args.output_file:
            print("错误: 使用--truth-file时必须指定--output-file")
            return False
        
        truth_file = Path(args.truth_file)
        output_file = Path(args.output_file)
        
        if not truth_file.exists():
            print(f"错误: 真实标准文件不存在: {truth_file}")
            return False
        
        if not output_file.exists():
            print(f"错误: 输出文件不存在: {output_file}")
            return False
    
    return True


def evaluate_directories(args: argparse.Namespace) -> dict:
    """评估整个目录"""
    if not args.quiet:
        print(f"正在匹配文件...")
        print(f"  Truth目录: {args.truth_dir}")
        print(f"  Output目录: {args.output_dir}")
    
    # 匹配文件
    matcher = FileMatcher(args.truth_dir, args.output_dir)
    
    try:
        file_pairs = matcher.find_file_pairs('.md')
        
        if not file_pairs:
            print("错误: 未找到匹配的文件对")
            return {'error': 'No matching file pairs found'}
        
        if not args.quiet:
            print(f"找到 {len(file_pairs)} 个文件对")
        
        # 创建评估器
        evaluator = Evaluator(
            text_normalize_whitespace=args.text_normalize_whitespace,
            text_ignore_case=args.text_ignore_case,
            table_structure_only=args.table_structure_only,
            table_ignore_nodes=args.table_ignore_nodes.split(',') if args.table_ignore_nodes else None
        )
        
        # 批量评估
        if not args.quiet:
            print("开始评估...")
        
        results = evaluator.evaluate_batch(file_pairs)
        
        return results
        
    except Exception as e:
        error_msg = f"评估过程中发生错误: {str(e)}"
        print(error_msg)
        return {'error': error_msg}


def evaluate_single_file(args: argparse.Namespace) -> dict:
    """评估单个文件对"""
    if not args.quiet:
        print(f"正在评估文件对:")
        print(f"  Truth文件: {args.truth_file}")
        print(f"  Output文件: {args.output_file}")
    
    # 创建评估器
    evaluator = Evaluator(
        text_normalize_whitespace=args.text_normalize_whitespace,
        text_ignore_case=args.text_ignore_case,
        table_structure_only=args.table_structure_only,
        table_ignore_nodes=args.table_ignore_nodes.split(',') if args.table_ignore_nodes else None
    )
    
    # 评估单个文件对
    result = evaluator.evaluate_file_pair(args.truth_file, args.output_file)
    
    return {
        'total_files': 1,
        'individual_results': [result],
        'summary': {
            'valid_files': 1 if 'error' not in result else 0,
            'failed_files': 1 if 'error' in result else 0
        }
    }


def print_results(results: dict, detailed: bool = False, quiet: bool = False):
    """打印评估结果"""
    if 'error' in results:
        print(f"评估失败: {results['error']}")
        return
    
    # 打印汇总信息
    if not quiet:
        print("\n" + "="*60)
        print("评估结果汇总")
        print("="*60)
        
        print(f"总文件数: {results['total_files']}")
        
        if 'summary' in results:
            summary = results['summary']
            if 'error' not in summary:
                print(f"成功评估: {summary['valid_files']}")
                print(f"评估失败: {summary['failed_files']}")
                
                if 'overall_metrics' in summary:
                    overall = summary['overall_metrics']
                    print(f"\n综合评估分数:")
                    print(f"  平均分数: {overall['avg_weighted_score']:.4f}")
                    print(f"  最高分数: {overall['max_weighted_score']:.4f}")
                    print(f"  最低分数: {overall['min_weighted_score']:.4f}")
                
                if 'text_metrics' in summary:
                    text = summary['text_metrics']
                    print(f"\n文本评估 (NED):")
                    print(f"  平均分数: {text['avg_ned_score']:.4f}")
                    print(f"  最高分数: {text['max_ned_score']:.4f}")
                    print(f"  最低分数: {text['min_ned_score']:.4f}")
                
                if 'table_metrics' in summary:
                    table = summary['table_metrics']
                    print(f"\n表格评估 (TEDS):")
                    print(f"  平均分数: {table['avg_teds_score']:.4f}")
                    print(f"  最高分数: {table['max_teds_score']:.4f}")
                    print(f"  最低分数: {table['min_teds_score']:.4f}")
    
    # 打印详细结果
    if detailed and 'individual_results' in results:
        print("\n" + "="*60)
        print("详细评估结果")
        print("="*60)
        
        for i, result in enumerate(results['individual_results'], 1):
            print(f"\n文件对 {i}:")
            
            if 'error' in result:
                print(f"  错误: {result['error']}")
                continue
            
            files = result['files']
            print(f"  Truth: {Path(files['truth']).name}")
            print(f"  Output: {Path(files['output']).name}")
            
            if 'overall_score' in result:
                overall = result['overall_score']
                print(f"  综合分数: {overall['weighted_score']:.4f}")
                print(f"  文本分数: {overall['text_score']:.4f}")
                print(f"  表格分数: {overall['table_score']:.4f}")
            
            if 'content_stats' in result:
                stats = result['content_stats']
                print(f"  内容统计:")
                print(f"    Truth - 文本长度: {stats['truth']['text_length']}, 表格数: {stats['truth']['table_count']}")
                print(f"    Output - 文本长度: {stats['output']['text_length']}, 表格数: {stats['output']['table_count']}")


def save_results(results: dict, output_file: str):
    """保存结果到JSON文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"\n结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存结果失败: {str(e)}")


def main():
    """主函数"""
    args = parse_arguments()
    
    # 验证参数
    if not validate_arguments(args):
        sys.exit(1)
    
    # 执行评估
    if args.truth_dir:
        results = evaluate_directories(args)
    else:
        results = evaluate_single_file(args)
    
    # 显示结果
    print_results(results, detailed=args.detailed, quiet=args.quiet)
    
    # 保存结果
    if args.output_json:
        save_results(results, args.output_json)
    
    # 返回适当的退出码
    if 'error' in results:
        sys.exit(1)
    elif 'summary' in results and results['summary'].get('failed_files', 0) > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
