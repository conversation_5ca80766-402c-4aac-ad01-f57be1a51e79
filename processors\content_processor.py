"""
内容处理核心逻辑模块
"""
from typing import List, Dict, Any, Optional
from utils.logger import get_logger
from processors.ai_services import llm_service
from processors.table_merger import table_merger

logger = get_logger(__name__)


class ContentProcessor:
    """内容处理器"""
    
    def __init__(self):
        """初始化内容处理器"""
        self.llm_service = llm_service
        self.table_merger = table_merger
        logger.info("内容处理器初始化完成")
    
    def process_content_list(self, content_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理完整的content_list
        
        Args:
            content_list: 原始内容列表
            
        Returns:
            处理后的内容列表
        """
        logger.info(f"开始处理content_list，包含 {len(content_list)} 个元素")
        
        # 创建副本避免修改原数据
        processed_list = content_list.copy()
        
        # 处理页面衔接
        processed_list = self._process_page_transitions(processed_list)
        
        logger.info(f"处理完成，最终包含 {len(processed_list)} 个元素")
        return processed_list
    
    def _process_page_transitions(self, content_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理页面衔接
        
        Args:
            content_list: 内容列表
            
        Returns:
            处理后的内容列表
        """
        logger.info("开始处理页面衔接")
        
        i = 0
        transitions_processed = 0
        
        while i < len(content_list) - 1:
            current = content_list[i]
            next_item = content_list[i + 1]
            
            # 检查是否为页面衔接点
            if self._is_page_transition(current, next_item):
                logger.debug(f"发现页面衔接点: 第{i}和{i+1}个元素")
                
                # 处理不同类型的衔接
                if self._is_continued_table(current, next_item):
                    # 续表处理
                    merged_table = self._handle_table_continuation(current, next_item)
                    if merged_table:
                        content_list[i] = merged_table
                        content_list.pop(i + 1)
                        transitions_processed += 1
                        logger.info(f"成功合并续表 (第{transitions_processed}个)")

                        # 检查是否可以继续与下一个表格合并（链式合并）
                        logger.debug(f"检查链式合并机会，当前位置i={i}, 列表长度={len(content_list)}")
                        while i < len(content_list) - 1:
                            current_merged = content_list[i]
                            next_candidate = content_list[i + 1]

                            logger.debug(f"检查链式合并: 第{i}个(页面{current_merged.get('page_idx')})和第{i+1}个(页面{next_candidate.get('page_idx')})元素")

                            # 检查合并后的表格是否可以与下一个表格继续合并
                            is_page_trans = self._is_page_transition(current_merged, next_candidate)
                            is_continued = self._is_continued_table(current_merged, next_candidate)

                            logger.debug(f"链式合并条件: 页面衔接={is_page_trans}, 续表={is_continued}")

                            if is_page_trans and is_continued:
                                logger.info(f"发现链式合并机会: 第{i}和{i+1}个元素")
                                chain_merged = self._handle_table_continuation(current_merged, next_candidate)
                                if chain_merged:
                                    content_list[i] = chain_merged
                                    content_list.pop(i + 1)
                                    transitions_processed += 1
                                    logger.info(f"成功链式合并续表 (第{transitions_processed}个)")
                                    logger.debug(f"链式合并后长度: {len(chain_merged.get('table_body', ''))}")
                                else:
                                    logger.debug("链式合并失败，停止")
                                    break  # 链式合并失败，停止
                            else:
                                logger.debug("不满足链式合并条件，停止")
                                break  # 不满足链式合并条件，停止

                        continue  # 不增加i，因为可能还有其他类型的衔接
                
                elif self._should_join_text(current, next_item):
                    # 文本拼接处理
                    joined_text = self._handle_text_joining(current, next_item)
                    if joined_text:
                        content_list[i] = joined_text
                        content_list.pop(i + 1)
                        transitions_processed += 1
                        logger.info(f"成功拼接文本 (第{transitions_processed}个)")
                        continue
            
            i += 1
        
        logger.info(f"页面衔接处理完成，共处理 {transitions_processed} 个衔接点")
        return content_list
    
    def _is_page_transition(self, current: Dict[str, Any], next_item: Dict[str, Any]) -> bool:
        """
        判断是否为页面衔接点
        
        Args:
            current: 当前元素
            next_item: 下一个元素
            
        Returns:
            是否为页面衔接点
        """
        current_page = current.get('page_idx', 0)
        next_page = next_item.get('page_idx', 0)
        
        # 页码连续且不同
        return next_page - current_page == 1
    
    def _is_continued_table(self, current: Dict[str, Any], next_item: Dict[str, Any]) -> bool:
        """
        判断是否为续表
        
        Args:
            current: 当前元素
            next_item: 下一个元素
            
        Returns:
            是否为续表
        """
        # 必须都是表格类型
        if current.get('type') != 'table' or next_item.get('type') != 'table':
            return False
        
        # 必须是页面衔接
        if not self._is_page_transition(current, next_item):
            return False
        
        # 根据用户需求：如果页面衔接处上下都是表格且中间没有其他内容，就是续表
        logger.debug("识别为续表")
        return True
    
    def _should_join_text(self, current: Dict[str, Any], next_item: Dict[str, Any]) -> bool:
        """
        判断是否应该拼接文本
        
        Args:
            current: 当前元素
            next_item: 下一个元素
            
        Returns:
            是否应该拼接文本
        """
        # 必须都是文本类型
        if current.get('type') != 'text' or next_item.get('type') != 'text':
            return False
        
        # 必须都没有text_level字段（不是标题）
        if 'text_level' in current or 'text_level' in next_item:
            return False
        
        # 必须是页面衔接
        if not self._is_page_transition(current, next_item):
            return False
        
        logger.debug("需要判断文本拼接")
        return True
    
    def _handle_table_continuation(self, prev_table: Dict[str, Any], next_table: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理续表
        
        Args:
            prev_table: 上一页表格
            next_table: 下一页表格
            
        Returns:
            合并后的表格，失败时返回None
        """
        try:
            logger.info("处理续表合并")
            
            # 使用LLM判断是否需要合并首行
            should_merge_row = self.llm_service.should_merge_first_row(prev_table, next_table)
            
            if should_merge_row:
                logger.info("需要合并首行，使用智能合并")
                merged_table = self.table_merger.merge_tables_with_row_merge(prev_table, next_table, merge_first_row=True)
            else:
                logger.info("不需要合并首行，使用基础合并")
                merged_table = self.table_merger.merge_tables_basic(prev_table, next_table)
            
            return merged_table
            
        except Exception as e:
            logger.error(f"续表处理失败: {e}")
            return None
    
    def _handle_text_joining(self, prev_text: Dict[str, Any], next_text: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理文本拼接
        
        Args:
            prev_text: 上一页文本
            next_text: 下一页文本
            
        Returns:
            拼接后的文本，失败时返回None
        """
        try:
            logger.info("处理文本拼接")
            
            # 使用LLM判断是否需要拼接
            should_join = self.llm_service.should_join_text(prev_text, next_text)
            
            if should_join:
                logger.info("需要拼接文本")
                joined_text = prev_text.copy()
                joined_text['text'] = prev_text['text'].strip() + ' ' + next_text['text'].strip()
                return joined_text
            else:
                logger.info("不需要拼接文本")
                return None
            
        except Exception as e:
            logger.error(f"文本拼接处理失败: {e}")
            return None
    
    def get_processing_stats(self, original_list: List[Dict[str, Any]], 
                           processed_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Args:
            original_list: 原始列表
            processed_list: 处理后列表
            
        Returns:
            统计信息
        """
        original_count = len(original_list)
        processed_count = len(processed_list)
        
        # 统计各类型元素数量
        original_tables = sum(1 for item in original_list if item.get('type') == 'table')
        original_texts = sum(1 for item in original_list if item.get('type') == 'text')
        
        processed_tables = sum(1 for item in processed_list if item.get('type') == 'table')
        processed_texts = sum(1 for item in processed_list if item.get('type') == 'text')
        
        # 计算页面范围
        pages = set(item.get('page_idx', 0) for item in original_list)
        page_range = f"{min(pages)}-{max(pages)}" if pages else "0"
        
        stats = {
            'original_count': original_count,
            'processed_count': processed_count,
            'elements_merged': original_count - processed_count,
            'original_tables': original_tables,
            'processed_tables': processed_tables,
            'tables_merged': original_tables - processed_tables,
            'original_texts': original_texts,
            'processed_texts': processed_texts,
            'texts_joined': original_texts - processed_texts,
            'page_range': page_range,
            'total_pages': len(pages)
        }
        
        return stats


# 创建全局内容处理器实例
content_processor = ContentProcessor()
