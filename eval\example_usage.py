"""
评估系统使用示例
"""

from pathlib import Path
from .evaluator import Evaluator
from .file_matcher import FileMatcher


def example_single_file_evaluation():
    """示例：单文件评估"""
    print("=== 单文件评估示例 ===")
    
    # 检查文件是否存在
    truth_file = Path("truth/data.md")
    output_file = Path("output/data.md")
    
    if not truth_file.exists() or not output_file.exists():
        print("示例文件不存在，跳过单文件评估示例")
        return
    
    # 创建评估器
    evaluator = Evaluator()
    
    # 评估单个文件对
    result = evaluator.evaluate_file_pair(truth_file, output_file)
    
    # 显示结果
    if 'error' in result:
        print(f"评估失败: {result['error']}")
        return
    
    print(f"文件对: {truth_file.name} <-> {output_file.name}")
    print(f"文本分数 (NED): {result['text_evaluation']['ned_score']:.4f}")
    print(f"表格分数 (TEDS): {result['table_evaluation']['avg_teds_score']:.4f}")
    print(f"综合分数: {result['overall_score']['weighted_score']:.4f}")
    print()


def example_batch_evaluation():
    """示例：批量评估"""
    print("=== 批量评估示例 ===")
    
    # 检查目录是否存在
    truth_dir = Path("truth")
    output_dir = Path("output")
    
    if not truth_dir.exists() or not output_dir.exists():
        print("示例目录不存在，跳过批量评估示例")
        return
    
    # 匹配文件
    matcher = FileMatcher(truth_dir, output_dir)
    file_pairs = matcher.find_file_pairs()
    
    if not file_pairs:
        print("未找到匹配的文件对")
        return
    
    print(f"找到 {len(file_pairs)} 个文件对")
    
    # 创建评估器
    evaluator = Evaluator()
    
    # 批量评估
    results = evaluator.evaluate_batch(file_pairs)
    
    # 显示汇总结果
    if 'error' in results:
        print(f"批量评估失败: {results['error']}")
        return
    
    summary = results['summary']
    print(f"成功评估: {summary['valid_files']} 个文件")
    print(f"平均文本分数: {summary['text_metrics']['avg_ned_score']:.4f}")
    print(f"平均表格分数: {summary['table_metrics']['avg_teds_score']:.4f}")
    print(f"平均综合分数: {summary['overall_metrics']['avg_weighted_score']:.4f}")
    print()


def example_custom_configuration():
    """示例：自定义配置"""
    print("=== 自定义配置示例 ===")
    
    truth_file = Path("truth/data.md")
    output_file = Path("output/data.md")
    
    if not truth_file.exists() or not output_file.exists():
        print("示例文件不存在，跳过自定义配置示例")
        return
    
    # 创建带自定义配置的评估器
    evaluator = Evaluator(
        text_normalize_whitespace=True,  # 标准化空白字符
        text_ignore_case=True,          # 忽略大小写
        table_structure_only=False      # 评估表格内容和结构
    )
    
    result = evaluator.evaluate_file_pair(truth_file, output_file)
    
    if 'error' not in result:
        print("自定义配置评估结果:")
        print(f"  文本分数: {result['text_evaluation']['ned_score']:.4f}")
        print(f"  表格分数: {result['table_evaluation']['avg_teds_score']:.4f}")
        print(f"  综合分数: {result['overall_score']['weighted_score']:.4f}")
        
        # 显示配置信息
        config = result['text_evaluation']['preprocessing_config']
        print(f"  配置: 标准化空白={config['normalize_whitespace']}, 忽略大小写={config['ignore_case']}")
    print()


def example_detailed_analysis():
    """示例：详细分析"""
    print("=== 详细分析示例 ===")
    
    truth_file = Path("truth/data.md")
    output_file = Path("output/data.md")
    
    if not truth_file.exists() or not output_file.exists():
        print("示例文件不存在，跳过详细分析示例")
        return
    
    evaluator = Evaluator()
    result = evaluator.evaluate_file_pair(truth_file, output_file)
    
    if 'error' in result:
        print(f"评估失败: {result['error']}")
        return
    
    # 详细文本分析
    text_eval = result['text_evaluation']
    print("文本评估详情:")
    print(f"  NED分数: {text_eval['ned_score']:.4f}")
    print(f"  编辑距离: {text_eval['edit_distance']}")
    print(f"  字符准确率: {text_eval['char_accuracy']:.4f}")
    print(f"  长度比例: {text_eval['length_stats']['length_ratio']:.4f}")
    
    # 详细表格分析
    table_eval = result['table_evaluation']
    print("\n表格评估详情:")
    print(f"  表格总数: {table_eval['table_count']}")
    print(f"  平均TEDS分数: {table_eval['avg_teds_score']:.4f}")
    print(f"  缺失表格: {table_eval['missing_tables']}")
    print(f"  多余表格: {table_eval['extra_tables']}")
    
    # 显示每个表格的分数
    print("  各表格分数:")
    for score_info in table_eval['individual_scores'][:5]:  # 只显示前5个
        print(f"    表格{score_info['table_index']}: {score_info['teds_score']:.4f}")
    
    # 综合分析
    overall = result['overall_score']
    print(f"\n综合评估:")
    print(f"  加权分数: {overall['weighted_score']:.4f}")
    print(f"  权重分配: 文本={overall['weights']['text_weight']:.1f}, 表格={overall['weights']['table_weight']:.1f}")
    print()


def main():
    """运行所有示例"""
    print("MinERU 评估系统使用示例")
    print("=" * 50)
    
    example_single_file_evaluation()
    example_batch_evaluation()
    example_custom_configuration()
    example_detailed_analysis()
    
    print("=" * 50)
    print("示例运行完成！")
    print("\n更多用法请参考:")
    print("  python -m eval.eval_main --help")
    print("  eval/README.md")


if __name__ == "__main__":
    main()
