"""
配置文件 - MinERU Content List 处理器
"""

import os
from pathlib import Path

# 阿里云API配置
QWEN_LLM_API_KEY = "sk-6119bb6e5a95440598ef80c25a40dc8e"
QWEN_LLM_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"

# 文件路径配置
PROJECT_ROOT = Path(__file__).parent
LIST_DIR = PROJECT_ROOT / "list"
OUTPUT_DIR = PROJECT_ROOT / "output"
TEMP_DIR = PROJECT_ROOT / "tmp"
PROMPTS_DIR = PROJECT_ROOT / "prompts"

# 确保目录存在
OUTPUT_DIR.mkdir(exist_ok=True)
TEMP_DIR.mkdir(exist_ok=True)
PROMPTS_DIR.mkdir(exist_ok=True)

# LLM配置
LLM_MODEL = "qwen-plus"  # 或者 qwen-turbo, qwen-max
LLM_TEMPERATURE = 0.1
LLM_MAX_TOKENS = 2000
LLM_TIMEOUT = 120  # 秒

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 1  # 秒

# 处理配置
DEFAULT_CONTEXT_LINES = 3  # 提取表格行数用于判断
MAX_TEXT_LENGTH_FOR_JOIN = 200  # 文本拼接判断的最大长度

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOG_FILE = PROJECT_ROOT / "logs" / "processor.log"

# 确保日志目录存在
LOG_FILE.parent.mkdir(exist_ok=True)

# Markdown配置
MARKDOWN_TABLE_ALIGN = "left"  # left, center, right
MARKDOWN_LINE_BREAK = "\n\n"

# 提示词配置
PROMPTS_FILE = PROMPTS_DIR / "llm_prompts.md"

# 调试配置
DEBUG_MODE = False
SAVE_INTERMEDIATE_FILES = True  # 是否保存中间处理文件
